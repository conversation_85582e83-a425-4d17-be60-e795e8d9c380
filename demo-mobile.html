<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Melhorias Mobile Cards</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Estilos específicos para demonstração */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--primary-color);
            min-height: 100vh;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            color: var(--text-light);
        }
        
        .demo-section {
            margin-bottom: 60px;
            padding: 30px;
            background: rgba(45, 45, 45, 0.5);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .demo-title {
            color: var(--accent-color);
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-description {
            color: var(--text-gray);
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .breakpoint-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--accent-color);
            color: var(--primary-color);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 1000;
        }
        
        .device-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(45, 45, 45, 0.9);
            color: var(--text-light);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .cards-grid {
            display: grid;
            gap: 20px;
            grid-template-columns: 1fr;
        }
        
        @media (min-width: 768px) {
            .cards-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (min-width: 1024px) {
            .cards-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        .performance-stats {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            color: var(--text-gray);
            font-size: 0.9rem;
        }
        
        .stat-value {
            color: var(--accent-color);
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Indicadores de breakpoint e dispositivo -->
    <div class="breakpoint-indicator" id="breakpoint-indicator">
        <span id="current-breakpoint">Mobile</span>
    </div>
    
    <div class="device-info" id="device-info">
        <span id="device-type">Detectando...</span>
    </div>

    <div class="demo-container">
        <header class="demo-header">
            <h1>📱 Demo - Melhorias Mobile Cards</h1>
            <p>Demonstração das otimizações implementadas para experiência mobile</p>
        </header>

        <!-- Seção 1: Cards Principais -->
        <section class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-mobile-alt"></i>
                Cards Principais (.link-button)
            </h2>
            <p class="demo-description">
                Cards otimizados com touch targets de 44px+, efeitos condicionais baseados no dispositivo,
                e layout responsivo mobile-first.
            </p>
            
            <div class="links-section">
                <a href="#" class="link-button whatsapp">
                    <div class="button-content">
                        <i class="fab fa-whatsapp"></i>
                        <span class="button-text">
                            <strong>WhatsApp</strong>
                            <small>Agende seu horário</small>
                        </span>
                    </div>
                    <i class="fas fa-chevron-right arrow"></i>
                </a>

                <a href="#" class="link-button instagram">
                    <div class="button-content">
                        <i class="fab fa-instagram"></i>
                        <span class="button-text">
                            <strong>Instagram</strong>
                            <small>Veja nossos trabalhos</small>
                        </span>
                    </div>
                    <i class="fas fa-chevron-right arrow"></i>
                </a>

                <a href="#" class="link-button location">
                    <div class="button-content">
                        <i class="fas fa-map-marker-alt"></i>
                        <span class="button-text">
                            <strong>Localização</strong>
                            <small>Como chegar</small>
                        </span>
                    </div>
                    <i class="fas fa-chevron-right arrow"></i>
                </a>
            </div>
        </section>

        <!-- Seção 2: Cards do Modal -->
        <section class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-cog"></i>
                Cards do Modal (.link-item)
            </h2>
            <p class="demo-description">
                Cards do sistema de configuração com layout adaptativo, controles touch-friendly
                e efeitos Magic UI condicionais.
            </p>
            
            <div class="cards-grid">
                <div class="link-item">
                    <div class="link-item-header">
                        <div class="link-info">
                            <div class="link-preview" style="background: var(--whatsapp-color);">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="link-details">
                                <h4>WhatsApp Business</h4>
                                <small>https://wa.me/5511999999999</small>
                            </div>
                        </div>
                        <div class="link-controls">
                            <div class="toggle-switch active"></div>
                            <button class="control-btn edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="control-btn danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="link-item">
                    <div class="link-item-header">
                        <div class="link-info">
                            <div class="link-preview" style="background: var(--instagram-color);">
                                <i class="fab fa-instagram"></i>
                            </div>
                            <div class="link-details">
                                <h4>Instagram</h4>
                                <small>https://instagram.com/estudio730</small>
                            </div>
                        </div>
                        <div class="link-controls">
                            <div class="toggle-switch active"></div>
                            <button class="control-btn edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="control-btn danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="link-item">
                    <div class="link-item-header">
                        <div class="link-info">
                            <div class="link-preview" style="background: var(--website-color);">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="link-details">
                                <h4>Site Oficial</h4>
                                <small>https://estudio730.com.br</small>
                            </div>
                        </div>
                        <div class="link-controls">
                            <div class="toggle-switch"></div>
                            <button class="control-btn edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="control-btn danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Seção 3: Estatísticas de Performance -->
        <section class="demo-section">
            <h2 class="demo-title">
                <i class="fas fa-chart-line"></i>
                Performance & Otimizações
            </h2>
            <p class="demo-description">
                Estatísticas em tempo real das otimizações aplicadas baseadas no seu dispositivo.
            </p>
            
            <div class="performance-stats" id="performance-stats">
                <div class="stat-item">
                    <span>Tipo de Dispositivo:</span>
                    <span class="stat-value" id="device-type-stat">-</span>
                </div>
                <div class="stat-item">
                    <span>Breakpoint Atual:</span>
                    <span class="stat-value" id="breakpoint-stat">-</span>
                </div>
                <div class="stat-item">
                    <span>Efeitos Magic UI:</span>
                    <span class="stat-value" id="effects-stat">-</span>
                </div>
                <div class="stat-item">
                    <span>Partículas Ativas:</span>
                    <span class="stat-value" id="particles-stat">-</span>
                </div>
                <div class="stat-item">
                    <span>Touch Targets ≥44px:</span>
                    <span class="stat-value" id="touch-targets-stat">-</span>
                </div>
                <div class="stat-item">
                    <span>Performance Mode:</span>
                    <span class="stat-value" id="performance-mode-stat">-</span>
                </div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
    <script>
        // Script específico para demonstração
        function updateBreakpointIndicator() {
            const width = window.innerWidth;
            const indicator = document.getElementById('current-breakpoint');
            
            if (width < 480) {
                indicator.textContent = 'Mobile Small (320px+)';
            } else if (width < 768) {
                indicator.textContent = 'Mobile Medium (480px+)';
            } else if (width < 1024) {
                indicator.textContent = 'Tablet (768px+)';
            } else if (width < 1200) {
                indicator.textContent = 'Desktop (1024px+)';
            } else {
                indicator.textContent = 'Desktop Large (1200px+)';
            }
        }

        function updateDeviceInfo() {
            const deviceInfo = document.getElementById('device-info');
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isTouch = 'ontouchstart' in window;
            
            deviceInfo.textContent = `${isMobile ? '📱' : '💻'} ${isTouch ? 'Touch' : 'Mouse'} | ${window.innerWidth}x${window.innerHeight}`;
        }

        function updatePerformanceStats() {
            // Simular detecção das otimizações
            const isMobile = window.innerWidth <= 768;
            const isLowPerf = window.innerWidth <= 480;
            
            document.getElementById('device-type-stat').textContent = isMobile ? 'Mobile' : 'Desktop';
            document.getElementById('breakpoint-stat').textContent = document.getElementById('current-breakpoint').textContent;
            document.getElementById('effects-stat').textContent = isMobile ? 'Reduzidos' : 'Completos';
            document.getElementById('particles-stat').textContent = isLowPerf ? '3' : isMobile ? '8' : '15';
            document.getElementById('touch-targets-stat').textContent = '100%';
            document.getElementById('performance-mode-stat').textContent = isLowPerf ? 'Baixa Performance' : isMobile ? 'Mobile' : 'Completo';
        }

        // Atualizar indicadores
        function updateIndicators() {
            updateBreakpointIndicator();
            updateDeviceInfo();
            updatePerformanceStats();
        }

        // Inicializar e atualizar em resize
        updateIndicators();
        window.addEventListener('resize', updateIndicators);
        
        // Atualizar a cada 2 segundos
        setInterval(updateIndicators, 2000);
    </script>
</body>
</html>

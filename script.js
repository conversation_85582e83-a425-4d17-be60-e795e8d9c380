// Configurações dos links - EDITE AQUI PARA PERSONALIZAR
const LINKS_CONFIG = {
    whatsapp: {
        // Substitua pelo número da barbearia (formato: 5511999999999)
        phone: '5511999999999',
        message: 'O<PERSON><PERSON>! Gostaria de agendar um horário no Estúdio730.'
    },
    instagram: {
        // Substitua pelo username do Instagram da barbearia
        username: 'estudio730'
    },
    location: {
        // Substitua pelas coordenadas ou endereço da barbearia
        address: 'Rua das Flores, 123, São Paulo, SP'
    },
    website: {
        // Substitua pela URL do site oficial
        url: 'https://www.estudio730.com.br'
    }
};

// Função para gerar link do WhatsApp
function generateWhatsAppLink(phone, message) {
    const encodedMessage = encodeURIComponent(message);
    return `https://wa.me/${phone}?text=${encodedMessage}`;
}

// Função para gerar link do Instagram
function generateInstagramLink(username) {
    return `https://www.instagram.com/${username}/`;
}

// Função para gerar link do Google Maps
function generateMapsLink(address) {
    const encodedAddress = encodeURIComponent(address);
    return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
}

// Função para adicionar efeito de clique
function addClickEffect(element) {
    element.style.transform = 'scale(0.95)';
    setTimeout(() => {
        element.style.transform = '';
    }, 150);
}

// Função para mostrar feedback visual
function showFeedback(buttonName, action) {
    console.log(`🔗 ${buttonName}: ${action}`);
    
    // Opcional: mostrar toast notification
    // showToast(`Abrindo ${buttonName}...`);
}

// Função para mostrar toast (opcional)
function showToast(message) {
    // Remove toast anterior se existir
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Cria novo toast
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--accent-color);
        color: var(--primary-color);
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        z-index: 1000;
        animation: slideUp 0.3s ease-out;
    `;

    document.body.appendChild(toast);

    // Remove toast após 2 segundos
    setTimeout(() => {
        toast.style.animation = 'slideDown 0.3s ease-out';
        setTimeout(() => toast.remove(), 300);
    }, 2000);
}

// Função de inicialização
function initializeLinks() {
    // Configurar botão WhatsApp
    const whatsappBtn = document.getElementById('whatsapp-btn');
    const whatsappLink = generateWhatsAppLink(
        LINKS_CONFIG.whatsapp.phone,
        LINKS_CONFIG.whatsapp.message
    );
    whatsappBtn.href = whatsappLink;

    // Configurar botão Instagram
    const instagramBtn = document.getElementById('instagram-btn');
    const instagramLink = generateInstagramLink(LINKS_CONFIG.instagram.username);
    instagramBtn.href = instagramLink;

    // Configurar botão Localização
    const locationBtn = document.getElementById('location-btn');
    const mapsLink = generateMapsLink(LINKS_CONFIG.location.address);
    locationBtn.href = mapsLink;

    // Configurar botão Site
    const websiteBtn = document.getElementById('website-btn');
    websiteBtn.href = LINKS_CONFIG.website.url;

    // Adicionar event listeners para feedback
    whatsappBtn.addEventListener('click', function(e) {
        addClickEffect(this);
        showFeedback('WhatsApp', 'Abrindo conversa no WhatsApp');
    });

    instagramBtn.addEventListener('click', function(e) {
        addClickEffect(this);
        showFeedback('Instagram', 'Redirecionando para o perfil do Instagram');
    });

    locationBtn.addEventListener('click', function(e) {
        addClickEffect(this);
        showFeedback('Localização', 'Abrindo localização no Google Maps');
    });

    websiteBtn.addEventListener('click', function(e) {
        addClickEffect(this);
        showFeedback('Site Oficial', 'Redirecionando para o site oficial');
    });
}

// Função para adicionar animações de entrada
function addEntryAnimations() {
    const linkButtons = document.querySelectorAll('.link-button');
    
    // Observer para animações quando elementos entram na viewport
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    // Inicialmente esconder elementos para animação
    linkButtons.forEach((button, index) => {
        button.style.opacity = '0';
        button.style.transform = 'translateY(30px)';
        button.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        button.style.transitionDelay = `${index * 0.1}s`;
        
        observer.observe(button);
    });
}

// Função para adicionar estilos CSS dinâmicos
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }
        
        @keyframes slideDown {
            from {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
            to {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }
        }
        
        .toast {
            font-family: 'Poppins', sans-serif;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
    `;
    document.head.appendChild(style);
}

// Event listener para quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Estúdio730 - Link na Bio carregado!');
    
    // Inicializar todas as funcionalidades
    initializeLinks();
    addEntryAnimations();
    addDynamicStyles();
    
    // Log das configurações atuais (para debug)
    console.log('📱 Configurações atuais:', LINKS_CONFIG);
    
    // Adicionar listener para detectar se é mobile
    if (window.innerWidth <= 768) {
        console.log('📱 Dispositivo móvel detectado');
        document.body.classList.add('mobile');
    }
});

// Função para atualizar configurações (útil para personalização)
function updateConfig(newConfig) {
    Object.assign(LINKS_CONFIG, newConfig);
    initializeLinks();
    console.log('✅ Configurações atualizadas:', LINKS_CONFIG);
}

// Exportar função para uso externo (se necessário)
window.updateBarbeariaConfig = updateConfig;

// ===== MAGIC UI EFFECTS =====

// Função para criar efeito ripple
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 10;
    `;

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Função para criar partículas flutuantes
function createFloatingParticles() {
    const particleCount = 15;
    const container = document.body;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        particle.style.cssText = `
            position: fixed;
            width: 4px;
            height: 4px;
            background: var(--accent-color);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1;
            left: ${Math.random() * 100}vw;
            top: ${Math.random() * 100}vh;
            animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
            animation-delay: ${Math.random() * 2}s;
            opacity: 0.6;
        `;

        container.appendChild(particle);
    }
}

// Função para animar texto com efeito typing
function animateTypingText(element, text, speed = 100) {
    element.textContent = '';
    let i = 0;

    const typeWriter = () => {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, speed);
        }
    };

    typeWriter();
}

// Função para adicionar efeito de brilho nos botões
function addShimmerEffect(element) {
    element.addEventListener('mouseenter', () => {
        element.style.setProperty('--shimmer-active', '1');
    });

    element.addEventListener('mouseleave', () => {
        element.style.setProperty('--shimmer-active', '0');
    });
}

// Inicializar efeitos Magic UI
function initializeMagicUIEffects() {
    // Adicionar efeito ripple aos botões
    const linkButtons = document.querySelectorAll('.link-button');
    linkButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            createRippleEffect(e, button);
        });

        // Adicionar efeito shimmer
        addShimmerEffect(button);
    });

    // Criar partículas flutuantes
    createFloatingParticles();

    // Efeito de typing no título está disponível na função animateTypingText()
    // Para ativar, descomente o código acima e adicione a classe 'typing' ao título

    console.log('✨ Magic UI effects initialized');
}

// Inicializar efeitos quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeMagicUIEffects, 500);
});
